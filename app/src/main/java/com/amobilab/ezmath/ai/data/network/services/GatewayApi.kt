package com.amobilab.ezmath.ai.data.network.services


import amobi.module.common.CommApplication
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import amobi.module.openai.api.chat.ChatCompletion
import amobi.module.openai.api.chat.ChatCompletionRequest
import amobi.module.openai.api.chat.ChatMessage
import amobi.module.openai.api.chat.ChatRole
import amobi.module.openai.api.chat.ImagePart
import amobi.module.openai.api.chat.StreamOptions
import amobi.module.openai.api.chat.TextPart
import amobi.module.openai.api.model.ModelId
import amobi.module.openai.client.OpenAI
import amobi.module.openai.client.OpenAIHost
import android.content.Context
import android.graphics.Bitmap
import android.util.Base64
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.models.Chat
import com.amobilab.ezmath.ai.data.network.models.ChatResponse
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.BotType
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.utils.AppCheckUtils
import com.amobilab.ezmath.ai.values.Const
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.takeWhile
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream

class GatewayApi {
    companion object {
        private const val END_POINT = "https://ai-gateway-server.amobilab.com/v1/"
        private fun getModelName():String {
            return if (PrefAssist.getString(PrefConst.MODEL_AI) == ModelAiMode.GPT.name) {
                Const.AiModelName.GPT
            } else {
                Const.AiModelName.GEMINI
            }
        }
        private fun getPortkeyProvider():String {
            return if (PrefAssist.getString(PrefConst.MODEL_AI) == ModelAiMode.GPT.name) "openai"
            else "gemini"
        }

        val line1 = ".\nWhen presenting math formulas, strictly follow these rules:\n"
        val line2 = "- Use the notation \$...\$ for inline math.\n"
        val line3 = "- Use the notation \$\$...\$\$ for display math.\n"

        val systemPromptRenderMath = line1 + line2 + line3
    }

    private val isCollecting = MutableStateFlow(true)

    fun stopCollecting() {
        isCollecting.value = false
    }

    suspend fun getResponsesNoStream(prompts: String, bitmap: Bitmap? = null, mode: ChatQuestionMode): ChatResponse {
        debugLog("AI Get Response: $mode")

        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId)
        }

        val appCheckToken = AppCheckUtils.requestTokenSuspend() ?: run {
            debugLog("Failed to get App Check token")
            return ChatResponse(
                chat = Chat(
                    prompt = "",
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        }

        val openAI = OpenAI(
            "sk-xxx",
            host = OpenAIHost(baseUrl = END_POINT),
            headers = mutableMapOf(
                "x-portkey-provider" to "openai",
                "x-gemini-model-id" to getModelName(),
                "x-provider-bacha" to getPortkeyProvider(),
                "Content-Type" to "application/json",
                "X-Firebase-AppCheck" to appCheckToken,
            )
        )

        return try {
            val messages = mutableListOf(
                ChatMessage(
                    role = ChatRole.System,
                    content = systemInstruction + systemPromptRenderMath
                )
            )

            // Add user message with image if bitmap is provided
            if (bitmap != null) {
                messages.add(
                    ChatMessage(
                        role = ChatRole.User,
                        content = listOf(
                            TextPart(text = prompts),
                            ImagePart(url = "data:image/png;base64," + encodeToBase64(bitmap), detail = "low")
                        )
                    )
                )
            } else {
                messages.add(
                    ChatMessage(
                        role = ChatRole.User,
                        content = prompts
                    )
                )
            }

            val chatCompletionRequest = ChatCompletionRequest(
                model = ModelId(getModelName()),
                messages = messages,
                temperature = if (RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE)) 0.0 else 1.0,
                topP = if (RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE)) 0.0 else 1.0,
            )

            val completion: ChatCompletion = openAI.chatCompletion(chatCompletionRequest)

            val content = completion.choices.firstOrNull()?.message?.content ?: ""
            val promptTokens = completion.usage?.promptTokens ?: 0
            val completionTokens = completion.usage?.completionTokens ?: 0
            val totalTokens = completion.usage?.totalTokens ?: 0



            debugLog("tesst $content")
            debugLog("tesst $promptTokens")
            debugLog("tesst $completionTokens")
            debugLog("tesst $totalTokens")

            ChatResponse(
                chat = Chat(
                    prompt = content,
                    bitmap = null,
                    isFromUser = false,
                    isError = false,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = promptTokens,
                outputTokenCount = completionTokens,
                totalTokenCount = totalTokens
            )
        } catch (e: Exception) {
            debugLog("tesst ${e.message}")
            ChatResponse(
                chat = Chat(
                    prompt = "",
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        }
    }

    fun getResponseWithImage(
        context: Context,
        prompt: String,
        bitmap: Bitmap,
        mode: ChatQuestionMode
    ): Flow<ChatResponse> = flow {
        debugLog("AI Get Response: $mode")

        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId)
        }

        val appCheckToken = AppCheckUtils.requestTokenSuspend() ?: run {
            debugLog("Failed to get App Check token")
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
            return@flow
        }

        val openAI = OpenAI(
            "sk-xxx",
            host = OpenAIHost(baseUrl = END_POINT),
            headers = mutableMapOf(
                "x-portkey-provider" to "openai",
                "x-gemini-model-id" to getModelName(),
                "x-provider-bacha" to getPortkeyProvider(),
                "Content-Type" to "application/json",
                "X-Firebase-AppCheck" to appCheckToken,
            )
        )
        var isSentError = true
        try {
            val promptImage = when (mode) {
                ChatQuestionMode.Translate ->
                    context.getString(mode.promptImageId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

                else -> context.getString(mode.promptImageId)
            }

            val chatCompletionRequest = withContext(Dispatchers.IO) {
                val inputContent = listOf(
                    ChatMessage(
                        role = ChatRole.System,
                        content = systemInstruction  + systemPromptRenderMath,
                    ),
                    ChatMessage(
                        role = ChatRole.User,
                        content = listOf(
                            TextPart(text = "$promptImage: $prompt"),
                            ImagePart(url = "data:image/png;base64," + encodeToBase64(bitmap), detail = "low")
                        )
                    )
                )

                debugLog("inputContent ${inputContent}")
                ChatCompletionRequest(
                    model = ModelId(getModelName()),
                    streamOptions = StreamOptions(includeUsage = true),
                    messages = inputContent,
                    maxTokens = Const.MAX_OUTPUT_TOKENS,
                    temperature = if (RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE)) 0.0 else 1.0,
                    topP = if (RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE)) 0.0 else 1.0,
                )
            }

            openAI.chatCompletions(chatCompletionRequest).collect { response ->
                val content = response.choices.firstOrNull()?.delta?.content
                if (content != null) {
                    isSentError = false
                }

                emit(
                    ChatResponse(
                        chat = Chat(
                            prompt = content ?: "",
                            bitmap = null,
                            isFromUser = false,
                            isError = false,
                            botType = BotType.BOT_GPT
                        ),
                        inputTokenCount = response.usage?.promptTokens ?: 0,
                        outputTokenCount = response.usage?.completionTokens ?: 0,
                        totalTokenCount = response.usage?.totalTokens ?: 0
                    )
                )
            }

        } catch (e: Exception) {
            debugLog("Response error: ${e.message}")
            emit(
                ChatResponse(
                    chat = Chat(
                        prompt = if (isSentError) context.getString(R.string.errors_please_try_again) else "",
                        bitmap = null,
                        isFromUser = false,
                        isError = true,
                        botType = BotType.BOT_GPT
                    ),
                    inputTokenCount = 0,
                    outputTokenCount = 0,
                    totalTokenCount = 0
                )
            )
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
        }
    }.catch { e ->
        debugLog("Response error: ${e.message}")
        emit(
            ChatResponse(
                chat = Chat(
                    prompt = context.getString(R.string.errors_please_try_again),
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        )
        isCollecting.value = false
        withContext(Dispatchers.Main) {
            MixedUtils.showToast(context, R.string.errors_please_try_again)
        }
    }

    fun getResponses(context: Context, listChat: MutableList<Chat>, mode: ChatQuestionMode): Flow<ChatResponse?> = flow {
        debugLog("AI Get Response: $mode")

        debugLog("aaaaaaabb = ${listChat}")
        val appCheckToken = AppCheckUtils.requestTokenSuspend() ?: run {
            debugLog("Failed to get App Check token")
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
            return@flow
        }

        val openAI = OpenAI(
            "sk-xxx",
            host = OpenAIHost(baseUrl = END_POINT),
            headers = mutableMapOf(
                "x-portkey-provider" to "openai",
                "x-gemini-model-id" to getModelName(),
                "x-provider-bacha" to getPortkeyProvider(),
                "Content-Type" to "application/json",
                "X-Firebase-AppCheck" to appCheckToken,
            )
        )

        isCollecting.value = true
        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))

            else -> CommApplication.appContext.getString(mode.instructionId)
        }

        var isSentError = true
        try {
            val response = withContext(Dispatchers.IO) {
                ChatCompletionRequest(
                    model = ModelId(getModelName()),
                    streamOptions = StreamOptions(includeUsage = true),
                    maxTokens = Const.MAX_OUTPUT_TOKENS,
                    temperature = if (RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE)) 0.0 else 1.0,
                    topP = if (RconfAssist.getBoolean(RconfConst.IS_NOT_RANDOM_RESPONSE)) 0.0 else 1.0,
                    messages = listOf(
                        ChatMessage(
                            role = ChatRole.System,
                            content = systemInstruction + systemPromptRenderMath
                        )
                    ) + listChat.take(6).reversed().map { item ->
                        if (item.isFromUser) {
                            if (item.bitmap != null) {
                                ChatMessage(
                                    role = ChatRole.User,
                                    content = listOf(
                                        TextPart(text = item.prompt),
                                        ImagePart(url = "data:image/png;base64," + encodeToBase64(item.bitmap), detail = "low")
                                    ),
                                )
                            } else {
                                ChatMessage(
                                    role = ChatRole.User,
                                    content = item.prompt
                                )
                            }
                        } else {
                            ChatMessage(
                                role = ChatRole.Assistant,
                                content = item.prompt
                            )
                        }
                    },
                )
            }

            openAI.chatCompletions(response)
                .takeWhile { isCollecting.value }
                .onCompletion {
                    debugLog("onCompletion : xong")
                }
                .collect { response ->
                    debugLog("response ${response.choices.firstOrNull()?.delta?.content}")

                    val content = response.choices.firstOrNull()?.delta?.content
                    if (content != null) {
                        isSentError = false
                    }
                    emit(
                        ChatResponse(
                            chat = Chat(
                                prompt = content ?: "",
                                bitmap = null,
                                isFromUser = false,
                                isError = false,
                                botType = BotType.BOT_GPT
                            ),
                            inputTokenCount = response.usage?.promptTokens ?: 0,
                            outputTokenCount = response.usage?.completionTokens ?: 0,
                            totalTokenCount = response.usage?.totalTokens ?: 0
                        )
                    )

                    debugLog("response2 inputTokenCount ${response.usage?.promptTokens} outputTokenCount ${response.usage?.completionTokens} totalTokenCount ${response.usage?.totalTokens} , response ${response.toString()}")
                }
        } catch (e: Exception) {
            debugLog("Response error stream: ${e.message}")
            try {
                emit(
                    ChatResponse(
                        chat = Chat(
                            prompt = if (isSentError) context.getString(R.string.errors_please_try_again) else "",
                            bitmap = null,
                            isFromUser = false,
                            isError = true,
                            botType = BotType.BOT_GPT
                        ),
                        inputTokenCount = 0,
                        outputTokenCount = 0,
                        totalTokenCount = 0
                    )
                )
            } catch (e: Exception) {
                debugLog("Response error stream: ${e.message}")
            }
            isCollecting.value = false
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
        }
    }.catch { e ->
        debugLog("Response error stream: ${e.message}")
        try {
            emit(
                ChatResponse(
                    chat = Chat(
                        prompt = context.getString(R.string.errors_please_try_again),
                        bitmap = null,
                        isFromUser = false,
                        isError = true,
                        botType = BotType.BOT_GPT
                    ),
                    inputTokenCount = 0,
                    outputTokenCount = 0,
                    totalTokenCount = 0
                )
            )
        } catch (e: Exception) {
            debugLog("Response error stream: ${e.message}")
        }
        isCollecting.value = false
        withContext(Dispatchers.Main) {
            MixedUtils.showToast(context, R.string.errors_please_try_again)
        }
    }

    private fun encodeToBase64(bitmap: Bitmap): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream)
        return Base64.encodeToString(byteArrayOutputStream.toByteArray(), Base64.NO_WRAP)
    }
}