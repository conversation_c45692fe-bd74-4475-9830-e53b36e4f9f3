<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gemini">Создать структуру</string>
    <string name="placeholder_default" translate_by="gpt">Введите ваш текст здесь…</string>
    <string name="essay_screen_description" translate_by="gpt">Конечно! Я могу помочь с этим. Пожалуйста, предоставьте некоторые детали для вашего эссе ниже.</string>
    <string name="label_choose_topic" translate_by="gpt">Выберите тему</string>
    <string name="label_essay_type" translate_by="gpt">Тип эссе</string>
    <string name="label_word_count" translate_by="gpt">Количество слов</string>
    <string name="label_language_tone" translate_by="gpt">Язык + тон</string>
    <string name="placeholder_topic" translate_by="gpt">Опишите место, которое приносит вам спокойствие</string>
    <string name="placeholder_essay_type" translate_by="gemini">Аргументативный, Повествовательный…</string>
    <string name="placeholder_word_count" translate_by="gemini">Например: 300 слов, 500 слов, 1000 слов…</string>
    <string name="placeholder_language_tone" translate_by="google">Пример: Формальный, академический, …</string>
    <string name="research_screen_description" translate_by="gpt">Место, где сырые данные превращаются в значимые визуальные истории через исследование и анализ.</string>
    <string name="label_research_topic" translate_by="gpt">Тема исследования</string>
    <string name="label_research_goal" translate_by="gpt">Цель исследования</string>
    <string name="label_preferred_sources" translate_by="gpt">Предпочтительные источники</string>
    <string name="label_depth_length" translate_by="gpt">Глубина / Длина</string>
    <string name="label_academic_level" translate_by="gpt">Академический уровень</string>
    <string name="placeholder_research_topic" translate_by="gpt">Изменение климата, Влияние ИИ на рабочие места, …</string>
    <string name="placeholder_research_goal" translate_by="google">Пример: Сбор информации, анализ тенденций …</string>
    <string name="placeholder_preferred_sources" translate_by="gpt">Научные журналы, книги, официальные статьи</string>
    <string name="placeholder_depth_length" translate_by="gemini">Например: 300 слов, 500 слов, 1000 слов…</string>
    <string name="placeholder_academic_level" translate_by="gpt">Пример: старшеклассники, студенты колледжа, углубленные исследования, …</string>
    <string name="literature_screen_description" translate_by="gemini">От слов к скрытым смыслам — мы помогаем вам раскрыть истинную ценность каждого литературного произведения.</string>
    <string name="label_title_of_work" translate_by="gpt">Название работы</string>
    <string name="label_author" translate_by="gpt">Автор</string>
    <string name="label_analysis_type" translate_by="gpt">Что вы хотите проанализировать?</string>
    <string name="label_format" translate_by="gpt">Длина / формат</string>
    <string name="placeholder_title" translate_by="gpt">Великий Гэтсби</string>
    <string name="placeholder_author" translate_by="gpt">Пример: Ф. Скотт Фицджеральд</string>
    <string name="placeholder_analysis_type" translate_by="gpt">Анализ персонажей, Основные темы…</string>
    <string name="placeholder_format" translate_by="gemini">Например: 300 слов, 500 слов, 1000 слов…</string>
    <string name="placeholder_academic_level_literature" translate_by="gpt">Пример: Средняя школа, старшая школа или университет, …</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 Тема исследования: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 Цель исследования: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 Предпочтительные источники: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 Глубина/Длина: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 Уровень образования: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gemini">🧾 Предлагаемый план:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. Введение</string>
    <string name="research_outline_introduction_overview" translate_by="gemini">Краткий обзор %1$s</string>
    <string name="research_outline_introduction_importance" translate_by="gemini">Важность исследований на %1$s уровне</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. Цели</string>
    <string name="research_outline_objectives_goal" translate_by="gemini">Уточните основную цель: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. Методология</string>
    <string name="research_outline_methodology_approach" translate_by="gpt">- Исследовательский подход</string>
    <string name="research_outline_methodology_sources" translate_by="gpt">- Источники данных: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gpt">4. Ключевые идеи</string>
    <string name="research_outline_key_insights_trends" translate_by="gemini">Обсуждайте тренды, факты или результаты анализа</string>
    <string name="research_outline_key_insights_citations" translate_by="google">- Используйте цитаты при необходимости</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. Заключение</string>
    <string name="research_outline_conclusion_summary" translate_by="gemini">Краткое изложение результатов</string>
    <string name="research_outline_conclusion_implications" translate_by="gpt">- Последствия или будущая работа</string>
    <string name="essay_outline_topic_label" translate_by="gpt">✏️ Тема эссе: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 Тип эссе: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 Количество слов: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ Язык и тон: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gemini">🧾 Предлагаемый план:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. Введение</string>
    <string name="essay_outline_introduction_topic" translate_by="google">- Представьте тему: %1 $ s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">- Предоставьте фон/контекст</string>
    <string name="essay_outline_introduction_thesis" translate_by="gpt">- Укажите тезис</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. Основные абзацы</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- Параграф 1: Первый аргумент или пункт</string>
    <string name="essay_outline_body_paragraph2" translate_by="gpt">- Параграф 2: Поддерживающие доказательства или повествование</string>
    <string name="essay_outline_body_paragraph3" translate_by="gpt">- Параграф 3: Контраргумент или дополнительная деталь</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. Заключение</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">- Подведите итоги ключевых моментов</string>
    <string name="essay_outline_conclusion_restate" translate_by="gpt">- Переформулируйте тезис по-другому</string>
    <string name="essay_outline_conclusion_final" translate_by="gemini">Завершите сильной заключительной мыслью</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ Заметки:</string>
    <string name="essay_outline_notes_tone" translate_by="gemini">Сохраняйте %1$s тон на протяжении всего текста</string>
    <string name="essay_outline_notes_wordcount" translate_by="gemini">Стремитесь к общему количеству примерно %1$s</string>
    <string name="essay_outline_notes_structure" translate_by="gemini">– Следуйте типичной структуре эссе %1$s</string>
    <string name="literature_outline_title_label" translate_by="gpt">Заголовок: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">Автор: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gpt">Фокус: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">Длина: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gemini">Уровень образования: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gpt">Контур:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. Введение</string>
    <string name="literature_outline_introduction_context" translate_by="gpt">Представьте литературное произведение и его контекст.</string>
    <string name="literature_outline_introduction_author" translate_by="gpt">Укажите автора и его актуальность для выбранного анализа.</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. Фон</string>
    <string name="literature_outline_background_summary" translate_by="gemini">Краткое содержание сюжета или ключевые персонажи (в зависимости от типа анализа).</string>
    <string name="literature_outline_background_context" translate_by="gpt">Предоставьте необходимый контекст для более глубокого анализа.</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. Основной анализ</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gpt">Глубокое погружение в: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gpt">Используйте доказательства из текста: цитаты, события, символизм и т. д.</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. Соединения</string>
    <string name="literature_outline_connections_themes" translate_by="gemini">Свяжите анализ со сквозными темами или реальными последствиями.</string>
    <string name="literature_outline_connections_contrast" translate_by="gpt">При желании сравните с другими персонажами или произведениями.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. Заключение</string>
    <string name="literature_outline_conclusion_insights" translate_by="gemini">Повторите основные выводы.</string>
    <string name="literature_outline_conclusion_value" translate_by="gemini">Оцените значимость работы с академической точки зрения.</string>
</resources>